/* Base styles for <PERSON><PERSON>gin - Mobile First Design */

:root {
    /* Light theme colors */
    --primary-color: #2c3e50;
    --primary-color-rgb: 44, 62, 80;
    --secondary-color: #3498db;
    --secondary-color-rgb: 52, 152, 219;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #ecf0f1;
    --light-color-rgb: 236, 240, 241;
    --dark-color: #34495e;
    --dark-color-rgb: 52, 73, 94;
    --background-color: #f5f7fa;
    --text-color: #333333;
    --border-color: #dfe6e9;
    --card-bg: #ffffff;
    --header-bg: #2c3e50;
    --footer-bg: #2c3e50;
    --mobile-nav-bg: #2c3e50;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Dark theme colors */
[data-bs-theme="dark"] {
    --primary-color: #3498db;
    --primary-color-rgb: 52, 152, 219;
    --secondary-color: #2c3e50;
    --secondary-color-rgb: 44, 62, 80;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #34495e;
    --light-color-rgb: 52, 73, 94;
    --dark-color: #ecf0f1;
    --dark-color-rgb: 236, 240, 241;
    --background-color: #1a1a2e;
    --text-color: #ecf0f1;
    --border-color: #34495e;
    --card-bg: #16213e;
    --header-bg: #0f3460;
    --footer-bg: #0f3460;
    --mobile-nav-bg: #0f3460;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Base styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: var(--transition);
    padding-top: 60px; /* Space for fixed header */
    padding-bottom: 70px; /* Space for mobile nav */
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    font-size: 16px; /* Increased base font size */
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header styles */
.app-header {
    background-color: var(--header-bg);
    box-shadow: var(--box-shadow);
    height: 60px;
    display: flex;
    align-items: center;
    z-index: 1030;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.app-title a {
    color: white;
    font-weight: bold;
    font-size: 1.3rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    text-decoration: none;
}

.user-info {
    color: white;
    margin-right: 15px;
    opacity: 0.9;
}

/* Mobile bottom navigation */
.mobile-nav {
    background-color: var(--mobile-nav-bg);
    box-shadow: 0 -2px 10px var(--shadow-color);
    height: 60px;
    z-index: 1020;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
}

.mobile-nav .nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.75rem 0; /* Increased padding */
    font-size: 0.9rem; /* Increased from 0.75rem */
    font-weight: 600; /* Added font weight */
    transition: var(--transition);
    flex: 1;
    text-decoration: none;
}

.mobile-nav .nav-link i {
    font-size: 1.5rem; /* Increased from 1.3rem */
    margin-bottom: 0.4rem; /* Increased spacing */
}

.mobile-nav .nav-link.active {
    color: white;
}

.mobile-nav .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Desktop navigation */
.app-header .nav-link {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    text-decoration: none;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
}

.app-header .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Main content area */
.app-content {
    flex: 1;
    padding: 2rem 0; /* Increased padding */
    margin-top: 1rem; /* Increased margin */
}

/* Footer styles */
.app-footer {
    background-color: var(--footer-bg);
    padding: 1rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Card styles */
.card {
    background-color: var(--card-bg);
    border: none;
    margin-bottom: 2rem; /* Increased margin */
    border-radius: 12px; /* Larger border radius */
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Enhanced shadow */
}

.card-header {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    font-size: 18px; /* Increased font size */
    padding: 1.5rem 1.75rem; /* Increased padding */
}

.card-body {
    padding: 1.75rem; /* Increased padding */
    font-size: 16px; /* Explicit font size */
    line-height: 1.6;
}

.card-footer {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-top: 1px solid var(--border-color);
    padding: 1.25rem 1.75rem; /* Increased padding */
    font-size: 15px;
}

/* Login page */
.login-container {
    max-width: 400px;
    margin: 0 auto;
}

/* Alert customization */
.alert {
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    padding: 1rem 1.25rem;
}

/* Form controls */
.form-control, .form-select {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem; /* Increased padding */
    font-size: 16px; /* Explicit font size */
    min-height: 48px; /* Minimum touch target */
    transition: var(--transition);
    line-height: 1.4;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.input-group-text {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
}

/* Button styling */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem; /* Increased padding */
    font-size: 16px; /* Explicit font size */
    font-weight: 600; /* Increased weight */
    min-height: 48px; /* Minimum touch target */
    transition: var(--transition);
    line-height: 1.4;
}

.btn-lg {
    padding: 1rem 2rem; /* Increased padding */
    font-size: 18px; /* Larger font */
    min-height: 56px; /* Larger touch target */
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 14px;
    min-height: 40px;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

/* Badge styling */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    border-radius: 50px;
}

/* Theme toggle */
.theme-toggle {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.form-check-input {
    cursor: pointer;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    cursor: pointer;
}

/* Welcome page specific styles */
.welcome-header {
    margin-bottom: 2rem;
}

.welcome-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Helper classes for colors */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Floating Action Button */
.floating-action-btn {
    position: fixed;
    bottom: 80px; /* Above mobile nav */
    right: 20px;
    z-index: 1030;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 4px 20px rgba(var(--primary-color-rgb), 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: fabPulse 2s infinite;
}

.floating-action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(var(--primary-color-rgb), 0.6);
}

.floating-action-btn .fab-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: white;
    text-decoration: none;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.floating-action-btn .fab-link:hover {
    color: white;
    transform: rotate(360deg);
}

@keyframes fabPulse {
    0%, 100% {
        box-shadow: 0 4px 20px rgba(var(--primary-color-rgb), 0.4);
    }
    50% {
        box-shadow: 0 4px 20px rgba(var(--primary-color-rgb), 0.6), 0 0 0 10px rgba(var(--primary-color-rgb), 0.1);
    }
}

/* Enhanced Mobile Navigation */
.mobile-nav {
    background: linear-gradient(135deg, var(--mobile-nav-bg), #1a252f);
    backdrop-filter: blur(10px);
    border-top: 2px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav .nav-link {
    position: relative;
    overflow: hidden;
}

.mobile-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.mobile-nav .nav-link:hover::before {
    left: 100%;
}

.mobile-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 12px 12px 0 0;
}

.mobile-nav .nav-link.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: var(--secondary-color);
    border-radius: 0 0 3px 3px;
}

/* Mobile Button Enhancements */
@media (max-width: 768px) {
    .btn {
        min-height: 52px; /* Increased from 44px */
        font-size: 17px; /* Increased font size */
        font-weight: 600;
        border-radius: 12px;
        padding: 0.875rem 1.5rem; /* Increased padding */
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .btn:active {
        transform: scale(0.95);
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-group .btn {
        min-height: 48px; /* Increased from 40px */
        border-radius: 8px;
        margin: 2px;
        font-size: 16px;
    }

    .btn-sm {
        min-height: 44px; /* Increased from 36px */
        padding: 0.75rem 1rem; /* Increased padding */
        font-size: 15px; /* Increased from 0.875rem */
    }

    /* Enhanced mobile cards */
    .card {
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: none;
        margin-bottom: 20px;
    }

    .card-header {
        border-radius: 16px 16px 0 0;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        padding: 16px 20px;
    }

    .card-body {
        padding: 20px;
    }

    /* Mobile form enhancements */
    .form-control, .form-select {
        min-height: 52px; /* Increased from 44px */
        font-size: 17px; /* Increased font size */
        padding: 1rem 1.25rem; /* Increased padding */
        border-radius: 12px;
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 3px rgba(var(--secondary-color-rgb), 0.1);
    }

    /* Mobile table improvements */
    .table-responsive {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .table th {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.8rem;
    }

    .table td {
        border-color: rgba(0, 0, 0, 0.05);
        padding: 12px 8px;
        vertical-align: middle;
    }

    /* Mobile badge enhancements */
    .badge {
        padding: 8px 16px; /* Increased padding */
        border-radius: 24px; /* Increased border radius */
        font-weight: 600;
        font-size: 0.85rem; /* Increased font size */
        text-transform: uppercase;
        letter-spacing: 0.5px;
        min-height: 32px; /* Added minimum height */
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* Mobile alert improvements */
    .alert {
        border-radius: 16px; /* Increased border radius */
        border: none;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12); /* Enhanced shadow */
        margin-bottom: 24px; /* Increased margin */
        padding: 1.25rem 1.5rem; /* Increased padding */
        font-size: 16px; /* Explicit font size */
        font-weight: 500;
    }

    /* Mobile spacing improvements */
    .container-fluid {
        padding-left: 20px; /* Increased from 16px */
        padding-right: 20px; /* Increased from 16px */
    }

    .row {
        margin-left: -10px; /* Increased from -8px */
        margin-right: -10px; /* Increased from -8px */
    }

    .col, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
    .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
        padding-left: 10px; /* Increased from 8px */
        padding-right: 10px; /* Increased from 8px */
    }

    /* Mobile typography improvements */
    h1 {
        font-size: 2.2rem; /* Increased */
        line-height: 1.3;
        margin-bottom: 20px;
        font-weight: 700;
    }

    h2 {
        font-size: 1.9rem; /* Increased */
        line-height: 1.3;
        margin-bottom: 18px;
        font-weight: 600;
    }

    h3 {
        font-size: 1.6rem; /* Increased */
        line-height: 1.3;
        margin-bottom: 16px;
        font-weight: 600;
    }

    h4, h5, h6 {
        font-size: 1.3rem; /* Increased */
        line-height: 1.3;
        margin-bottom: 16px;
        font-weight: 600;
    }

    p {
        font-size: 16px; /* Explicit size */
        line-height: 1.6;
        margin-bottom: 18px; /* Increased spacing */
    }

    /* Mobile search form improvements */
    .search-form .form-label {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .search-form .form-text {
        font-size: 0.8rem;
        color: var(--text-color);
        opacity: 0.7;
    }
}

/* Responsive adjustments */
@media (min-width: 992px) {
    .app-content {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .floating-action-btn {
        display: none; /* Hide on desktop */
    }
}
