<!-- Inventory Search Modal -->
<div class="modal fade" id="inventorySearchModal" tabindex="-1" aria-labelledby="inventorySearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="inventorySearchModalLabel">
                    <i class="fas fa-search me-2"></i>Search Inventory
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Search Form -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <form id="inventorySearchForm">
                                    <div class="row g-3">
                                        <div class="col-md-8">
                                            <label for="inventorySearchTerm" class="form-label">
                                                <i class="fas fa-search me-1"></i>Search Term
                                            </label>
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="inventorySearchTerm" 
                                                   placeholder="Enter item number or description (partial or full)"
                                                   autocomplete="off">
                                            <div class="form-text">
                                                Search by item number or description. Supports partial matching.
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label for="inventorySearchLimit" class="form-label">
                                                <i class="fas fa-list-ol me-1"></i>Limit
                                            </label>
                                            <select class="form-select" id="inventorySearchLimit">
                                                <option value="10">10 items</option>
                                                <option value="20" selected>20 items</option>
                                                <option value="50">50 items</option>
                                                <option value="100">100 items</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-grid">
                                                <button type="submit" class="btn btn-primary" id="inventorySearchBtn">
                                                    <i class="fas fa-search me-1"></i>Search
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search Results -->
                <div id="inventorySearchResults">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p class="mb-0">Enter a search term and click "Search" to find inventory items</p>
                        <small class="text-muted">Search by item number or description</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div class="text-muted small" id="inventorySearchInfo">
                        <!-- Search info will be displayed here -->
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Material Request Modal -->
<div class="modal fade" id="materialRequestModal" tabindex="-1" aria-labelledby="materialRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="materialRequestModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>Add Material Request
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Selected Item Info -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Selected Item</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>Item Number:</strong>
                                    <span id="selectedItemNum" class="text-primary">-</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Description:</strong>
                                    <span id="selectedItemDesc" class="text-muted">-</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>Location:</strong>
                                    <span id="selectedItemLocation" class="text-info">-</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Issue Unit:</strong>
                                    <span id="selectedItemUnit" class="text-success">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Request Form -->
                <form id="materialRequestForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="requestQuantity" class="form-label">
                                <i class="fas fa-hashtag me-1"></i>Quantity <span class="text-danger">*</span>
                            </label>
                            <input type="number"
                                   class="form-control"
                                   id="requestQuantity"
                                   min="0.01"
                                   step="0.01"
                                   value="1"
                                   required>
                            <div class="form-text">Enter the quantity needed</div>
                        </div>
                        <div class="col-md-6">
                            <label for="requestLocation" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>Location
                            </label>
                            <input type="text"
                                   class="form-control"
                                   id="requestLocation"
                                   placeholder="Location will be auto-filled when direct request is unchecked">
                            <div class="form-text">
                                <span id="locationHelpText">Not required for direct request</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="requestBy" class="form-label">
                                <i class="fas fa-user me-1"></i>Request By <span class="text-danger">*</span>
                            </label>
                            <input type="text"
                                   class="form-control"
                                   id="requestBy"
                                   value=""
                                   placeholder="Loading person ID..."
                                   required
                                   readonly>
                            <div class="form-text">Person ID will be automatically populated from your profile</div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input"
                                       type="checkbox"
                                       id="directRequest"
                                       checked>
                                <label class="form-check-label" for="directRequest">
                                    <strong>Direct Request</strong>
                                    <small class="text-muted d-block">Check for direct request (no location required), uncheck to specify location</small>
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="requestNotes" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>Notes (Optional)
                            </label>
                            <textarea class="form-control"
                                      id="requestNotes"
                                      rows="3"
                                      placeholder="Additional notes or comments for this request"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div class="text-muted small" id="materialRequestInfo">
                        <!-- Request info will be displayed here -->
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-primary" id="submitMaterialRequest">
                            <i class="fas fa-paper-plane me-1"></i>Submit Request
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Inventory Search Modal Styles */
.inventory-item-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: box-shadow 0.2s ease;
    background: white;
}

.inventory-item-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.inventory-item-header {
    background: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.inventory-item-body {
    padding: 15px;
}

.inventory-item-title {
    font-weight: 600;
    color: #495057;
    margin: 0;
    font-size: 1.2rem; /* Increased font size */
}

.inventory-item-subtitle {
    color: #6c757d;
    font-size: 1rem; /* Increased font size */
    margin-top: 5px;
}

.inventory-item-description {
    color: #495057;
    margin: 10px 0;
    font-size: 1rem; /* Increased font size */
    line-height: 1.4;
}

.inventory-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.inventory-detail-item {
    display: flex;
    flex-direction: column;
}

.inventory-detail-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 500;
    margin-bottom: 3px;
}

.inventory-detail-value {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.inventory-detail-value.highlight {
    color: #0d6efd;
    font-weight: 600;
}

.inventory-detail-value.success {
    color: #198754;
    font-weight: 600;
}

.inventory-detail-value.warning {
    color: #fd7e14;
    font-weight: 600;
}

.inventory-detail-value.danger {
    color: #dc3545;
    font-weight: 600;
}

.inventory-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.inventory-status-badge.active {
    background-color: #d1edff;
    color: #0c63e4;
}

.inventory-status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.inventory-loading {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.inventory-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

.inventory-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.inventory-error {
    text-align: center;
    padding: 40px 20px;
    color: #dc3545;
}

.search-stats {
    font-size: 0.85rem;
    color: #6c757d;
}

.search-stats .badge {
    font-size: 0.75rem;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .modal-xl {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .inventory-details-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .inventory-item-header {
        padding: 16px; /* Increased padding */
    }

    .inventory-item-body {
        padding: 16px; /* Increased padding */
    }

    .inventory-item-title {
        font-size: 1.1rem; /* Increased font size */
    }
}

@media (max-width: 576px) {
    .modal-dialog {
        margin: 5px;
        max-width: calc(100% - 10px);
    }
    
    .inventory-item-card {
        margin-bottom: 10px;
    }
    
    .inventory-details-grid {
        gap: 8px;
    }
}

/* Loading Animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.inventory-loading {
    animation: pulse 2s infinite;
}

/* Highlight Search Terms */
.search-highlight {
    background-color: #fff3cd;
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: 600;
}

/* Material Request Modal Styles */
.add-to-request-btn {
    transition: all 0.2s ease;
}

.add-to-request-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

#materialRequestModal .card-header {
    border-bottom: 1px solid #dee2e6;
}

#materialRequestModal .form-check-label small {
    font-size: 0.8rem;
    margin-top: 2px;
}

#materialRequestModal .text-primary {
    font-weight: 600;
}

#materialRequestModal .text-info {
    font-weight: 500;
}

#materialRequestModal .text-success {
    font-weight: 500;
}

/* Loading state for submit button */
#submitMaterialRequest.loading {
    pointer-events: none;
    opacity: 0.7;
}

#submitMaterialRequest.loading .fas {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error states */
.request-success {
    color: #198754;
    font-weight: 500;
}

.request-error {
    color: #dc3545;
    font-weight: 500;
}
</style>
