{% extends 'base.html' %}

{% block title %}Enhanced Work Orders - <PERSON><PERSON>{% endblock %}

{% block content %}

<style>
/* Responsive Typography Foundation */
:root {
    /* Base Typography Variables */
    --base-font-size: 16px;
    --small-font-size: 14px;
    --large-font-size: 18px;
    --xl-font-size: 20px;

    /* Button Typography */
    --button-font-size: 16px;
    --button-font-size-sm: 14px;
    --button-font-size-lg: 18px;

    /* Card Typography */
    --card-title-font-size: 20px;
    --card-subtitle-font-size: 16px;
    --card-body-font-size: 16px;
    --card-meta-label-font-size: 12px;
    --card-meta-value-font-size: 14px;

    /* Mobile Typography Scaling */
    --mobile-scale-factor: 1.1;
    --mobile-min-font-size: 14px;
    --mobile-button-min-size: 16px;

    /* Enhanced Work Orders Color Variables */
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Typography Improvements */
body {
    font-size: var(--base-font-size);
    line-height: 1.5;
}

/* Responsive Typography Scaling */
@media (max-width: 768px) {
    :root {
        --card-title-font-size: 18px;
        --card-subtitle-font-size: 15px;
        --card-body-font-size: 15px;
        --card-meta-label-font-size: 13px;
        --card-meta-value-font-size: 15px;
        --button-font-size: 16px;
        --button-font-size-sm: 15px;
    }
}

@media (max-width: 576px) {
    :root {
        --card-title-font-size: 17px;
        --card-subtitle-font-size: 15px;
        --card-body-font-size: 15px;
        --card-meta-label-font-size: 13px;
        --card-meta-value-font-size: 15px;
        --button-font-size: 16px;
        --button-font-size-sm: 15px;
    }
}

/* Desktop Typography Enhancements */
@media (min-width: 769px) {
    /* Improve desktop button readability */
    .btn {
        font-size: var(--button-font-size);
        font-weight: 500;
        line-height: 1.3;
        letter-spacing: 0.2px;
        min-height: 40px;
        padding: 0.5rem 1rem;
    }

    .btn-sm {
        font-size: var(--button-font-size-sm);
        min-height: 36px;
        padding: 0.375rem 0.75rem;
    }

    .btn-lg {
        font-size: var(--button-font-size-lg);
        min-height: 48px;
        padding: 0.75rem 1.5rem;
    }

    /* Desktop card action buttons */
    .card-action-btn {
        min-height: 72px;
        font-size: var(--button-font-size);
    }

    /* Desktop view toggle buttons */
    .card-header .btn-group .btn {
        min-height: 40px;
        padding: 0.5rem 1rem;
        font-size: var(--button-font-size-sm);
        font-weight: 500;
    }
}

/* Materials Badge Styling */
.materials-check-container {
    min-width: 80px;
    text-align: center;
}

.materials-check-btn {
    border: 1px solid #198754; /* Green border */
    background-color: #198754; /* Green background */
    color: white; /* White text and icon color */
    transition: all 0.2s ease;
}

.materials-check-btn:hover {
    background-color: #6c757d; /* Grey background on hover */
    border-color: #6c757d; /* Grey border on hover */
    color: white; /* Keep text and icon color white */
}

.materials-check-btn i, .materials-check-btn span {
    transition: color 0.2s ease, transform 0.2s ease;
}

.materials-badge {
    font-size: var(--small-font-size);
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    white-space: nowrap;
    cursor: help;
    transition: all 0.2s ease;
    font-weight: 500;
    line-height: 1.2;
    min-height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.materials-badge:hover {
    transform: scale(1.05);
}

.materials-badge.bg-success {
    background-color: #198754 !important;
    color: white;
    box-shadow: 0 2px 4px rgba(25, 135, 84, 0.3);
}

.materials-badge.bg-secondary {
    background-color: #6c757d !important;
    color: white;
}

.materials-badge.bg-danger {
    background-color: #dc3545 !important;
    color: white;
}

/* Mobile Responsive Materials */
@media (max-width: 768px) {
    .materials-badge {
        font-size: var(--mobile-min-font-size);
        padding: 0.5rem 0.625rem;
        min-height: 36px;
    }

    .materials-check-btn {
        padding: 0.5rem 0.75rem;
        font-size: var(--mobile-button-min-size);
        min-height: 44px;
        font-weight: 500;
    }
}

/* Mobile Work Order Action Buttons */
.mobile-action-grid {
    display: none;
}

@media (max-width: 768px) {
    .desktop-actions {
        display: none;
    }

    .mobile-action-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        padding: 8px;
    }

    .mobile-action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 12px 8px;
        border-radius: 12px;
        border: 2px solid;
        background: white;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-height: 70px;
        position: relative;
        overflow: hidden;
    }

    .mobile-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
    }

    .mobile-action-btn:hover::before {
        left: 100%;
    }

    .mobile-action-btn i {
        font-size: 1.75rem;
        margin-bottom: 6px;
        transition: transform 0.3s ease;
    }

    .mobile-action-btn span {
        font-size: var(--mobile-button-min-size);
        font-weight: 600;
        text-align: center;
        line-height: 1.3;
        letter-spacing: 0.3px;
    }

    .mobile-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .mobile-action-btn:hover i {
        transform: scale(1.1);
    }

    .mobile-action-btn:active {
        transform: translateY(0) scale(0.95);
    }

    /* Color variants for mobile buttons */
    .mobile-action-btn.btn-success {
        border-color: #198754;
        color: #198754;
    }

    .mobile-action-btn.btn-success:hover {
        background: #198754;
        color: white;
    }

    .mobile-action-btn.btn-primary {
        border-color: #0d6efd;
        color: #0d6efd;
    }

    .mobile-action-btn.btn-primary:hover {
        background: #0d6efd;
        color: white;
    }

    .mobile-action-btn.btn-warning {
        border-color: #ffc107;
        color: #856404;
    }

    .mobile-action-btn.btn-warning:hover {
        background: #ffc107;
        color: #856404;
    }

    .mobile-action-btn.btn-info {
        border-color: #0dcaf0;
        color: #055160;
    }

    .mobile-action-btn.btn-info:hover {
        background: #0dcaf0;
        color: white;
    }
}

/* Work Order Card Styles */
.work-order-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    background: white;
    position: relative;
}

.work-order-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.work-order-card-header {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 16px 20px;
    border-bottom: none;
    position: relative;
}

.work-order-card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #2ecc71, #3498db);
}

.work-order-number {
    font-size: var(--card-title-font-size);
    font-weight: 700;
    margin: 0;
    text-decoration: none;
    color: white;
    line-height: 1.3;
    letter-spacing: 0.5px;
}

.work-order-number:hover {
    color: #ecf0f1;
    text-decoration: none;
}

.work-order-site {
    font-size: var(--card-subtitle-font-size);
    opacity: 0.95;
    margin-top: 6px;
    font-weight: 500;
    line-height: 1.4;
}

.work-order-card-body {
    padding: 20px;
}

.work-order-description {
    font-size: var(--card-body-font-size);
    line-height: 1.6;
    color: #2c3e50;
    margin-bottom: 18px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-weight: 400;
    letter-spacing: 0.2px;
}

.work-order-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.meta-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 8px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
}

.meta-label {
    font-size: var(--card-meta-label-font-size);
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-bottom: 6px;
    font-weight: 700;
    line-height: 1.2;
}

.meta-value {
    font-size: var(--card-meta-value-font-size);
    color: #2c3e50;
    font-weight: 600;
    line-height: 1.4;
    letter-spacing: 0.2px;
}

.work-order-actions {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 16px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.card-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 14px 10px;
    border-radius: 12px;
    border: 2px solid;
    background: white;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 68px;
    font-size: var(--button-font-size-sm);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    line-height: 1.3;
    letter-spacing: 0.3px;
}

.card-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.card-action-btn:hover::before {
    left: 100%;
}

.card-action-btn i {
    font-size: 1.4rem;
    margin-bottom: 6px;
    transition: transform 0.3s ease;
}

.card-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-action-btn:hover i {
    transform: scale(1.1);
}

.card-action-btn:active {
    transform: translateY(0) scale(0.95);
}

/* Card action button variants */
.card-action-btn.btn-success {
    border-color: #198754;
    color: #198754;
}

.card-action-btn.btn-success:hover {
    background: #198754;
    color: white;
}

.card-action-btn.btn-primary {
    border-color: #0d6efd;
    color: #0d6efd;
}

.card-action-btn.btn-primary:hover {
    background: #0d6efd;
    color: white;
}

.card-action-btn.btn-warning {
    border-color: #ffc107;
    color: #856404;
}

.card-action-btn.btn-warning:hover {
    background: #ffc107;
    color: #856404;
}

.card-action-btn.btn-info {
    border-color: #0dcaf0;
    color: #055160;
}

.card-action-btn.btn-info:hover {
    background: #0dcaf0;
    color: white;
}

.work-order-checkbox {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 20px;
    height: 20px;
    z-index: 10;
}

/* Priority indicators */
.priority-indicator-card {
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    border-radius: 16px 0 0 16px;
}

.priority-1 { background: #e74c3c; }
.priority-2 { background: #f39c12; }
.priority-3 { background: #3498db; }
.priority-4 { background: #2ecc71; }
.priority-5 { background: #95a5a6; }

/* Table responsive improvements */
@media (max-width: 992px) {
    .table-responsive {
        font-size: var(--base-font-size);
    }

    .btn-group .btn {
        padding: 0.5rem 0.75rem;
        font-size: var(--button-font-size-sm);
        min-height: 44px;
        font-weight: 500;
        line-height: 1.3;
        letter-spacing: 0.2px;
    }

    .work-order-card {
        margin-bottom: 18px;
    }

    .work-order-meta {
        grid-template-columns: repeat(2, 1fr);
        gap: 14px;
    }

    .work-order-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
}

@media (max-width: 576px) {
    .work-order-meta {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .work-order-actions {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .card-action-btn {
        min-height: 56px;
        padding: 16px 12px;
        font-size: var(--mobile-button-min-size);
    }

    /* Mobile pagination improvements */
    .pagination .page-link {
        padding: 0.75rem 1rem;
        font-size: var(--mobile-button-min-size);
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        line-height: 1.2;
    }

    .pagination .page-item:not(.active) .page-link {
        border: 2px solid #dee2e6;
        background: white;
        color: #495057;
    }

    .pagination .page-item.active .page-link {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        font-weight: 600;
    }

    /* View toggle buttons */
    .btn-group .btn {
        border-radius: 8px !important;
        margin: 0 3px;
        padding: 0.75rem 1rem;
        font-size: var(--button-font-size-sm);
        min-height: 44px;
        font-weight: 500;
        line-height: 1.2;
        letter-spacing: 0.2px;
    }

    /* Header button improvements */
    .card-header .btn-outline-light {
        border-width: 2px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .card-header .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.2);
        border-color: white;
        transform: translateY(-1px);
    }

    .card-header .btn-light {
        background-color: white;
        color: var(--dark-color);
        border-color: white;
        font-weight: 600;
    }

    /* Mobile header adjustments */
    .card-header .d-flex {
        flex-wrap: wrap;
        gap: 12px;
    }

    .card-header h5 {
        flex: 1;
        min-width: 200px;
        font-size: var(--large-font-size);
        font-weight: 600;
        line-height: 1.3;
    }

    /* Mobile-specific typography enhancements */
    .mobile-action-btn {
        min-height: 76px;
        padding: 16px 10px;
    }

    .card-action-btn span {
        font-size: var(--mobile-button-min-size);
        font-weight: 600;
        line-height: 1.3;
        letter-spacing: 0.3px;
    }

    /* Improve touch targets for mobile */
    .work-order-checkbox {
        width: 20px;
        height: 20px;
        margin: 8px;
    }

    /* Better contrast for mobile readability */
    .meta-item {
        padding: 12px;
        background: rgba(0, 0, 0, 0.03);
        border-radius: 10px;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Accessibility improvements */
    .work-order-description {
        color: #1a1a1a;
        font-weight: 500;
    }

    .meta-value {
        color: #1a1a1a;
        font-weight: 600;
    }

    /* Enhanced button contrast */
    .card-action-btn:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    .mobile-action-btn:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }
}
</style>
<div class="welcome-header text-center mb-4">
    <h2 class="fw-bold">
        <i class="fas fa-clipboard-list me-2 text-primary"></i>Enhanced Work Orders
    </h2>
    <div class="badge bg-warning text-dark mb-2">Test (UAT) Environment</div>
    <div class="badge bg-primary text-white mb-3">
        <i class="fas fa-tachometer-alt me-1"></i>Lightning Fast Search
    </div>
    {% if user_site_id %}
    <div class="badge bg-info text-white mb-3">
        <i class="fas fa-map-marker-alt me-1"></i>Site: {{ user_site_id }}
    </div>
    {% endif %}
</div>

<!-- Search Filters Card -->
<div class="card border-0 shadow-sm mb-4 border-start border-primary border-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>Search Work Orders
        </h5>
    </div>
    <div class="card-body p-4">
        <form id="searchForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="siteFilter" class="form-label">Sites</label>
                    <select class="form-select" id="siteFilter" name="site_ids" multiple>
                        <option value="">Loading sites...</option>
                    </select>
                    <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple sites</small>
                </div>
                <div class="col-md-2">
                    <label for="statusFilter" class="form-label">Status</label>
                    <select class="form-select" id="statusFilter" name="status">
                        <option value="">All Statuses</option>
                        <option value="APPR">APPR - Approved</option>
                        <option value="ASSIGN">ASSIGN - Assigned</option>
                        <option value="READY">READY - Ready</option>
                        <option value="INPRG">INPRG - In Progress</option>
                        <option value="PACK">PACK - Packed</option>
                        <option value="DEFER">DEFER - Deferred</option>
                        <option value="WAPPR">WAPPR - Waiting Approval</option>
                        <option value="WGOVT">WGOVT - Waiting Government</option>
                        <option value="AWARD">AWARD - Awarded</option>
                        <option value="MTLCXD">MTLCXD - Material Cancelled</option>
                        <option value="MTLISD">MTLISD - Material Issued</option>
                        <option value="PISSUE">PISSUE - Parts Issue</option>
                        <option value="RTI">RTI - Ready to Issue</option>
                        <option value="WMATL">WMATL - Waiting Material</option>
                        <option value="WSERV">WSERV - Waiting Service</option>
                        <option value="WSCH">WSCH - Waiting Schedule</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="priorityFilter" class="form-label">Priority</label>
                    <select class="form-select" id="priorityFilter" name="priority">
                        <option value="">All Priorities</option>
                        <option value="1">1 - Critical</option>
                        <option value="2">2 - High</option>
                        <option value="3">3 - Medium</option>
                        <option value="4">4 - Low</option>
                        <option value="5">5 - Lowest</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="woclassFilter" class="form-label">Type</label>
                    <select class="form-select" id="woclassFilter" name="woclass">
                        <option value="WORKORDER">Work Orders</option>
                        <option value="ACTIVITY">Activities</option>
                        <option value="BOTH">Both</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="wonumFilter" class="form-label">Work Order #</label>
                    <input type="text" class="form-control" id="wonumFilter" name="wonum"
                           placeholder="e.g. 2021-1744762 or 1744762">
                    <small class="form-text text-muted">Exact or partial match</small>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Search
                        </button>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-12">
                    <label for="descriptionFilter" class="form-label">Description</label>
                    <input type="text" class="form-control" id="descriptionFilter" name="description"
                           placeholder="Search in description...">
                </div>
            </div>
        </form>

        <div class="row mt-3">
            <div class="col-12">
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Lazy Loading:</strong> Enter search criteria above to find work orders.
                    Results are sorted by Report Date (ascending) and limited to 20 records per page for optimal performance.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Results Container -->
<div id="searchResults" style="display: none;">
    <!-- Performance Metrics Card -->
    <div class="card border-0 shadow-sm mb-4 border-start border-success border-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-chart-line me-2"></i>Search Performance
            </h5>
        </div>
        <div class="card-body p-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-primary mb-1" id="searchTime">-</div>
                        <small class="text-muted">Search Time</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-success mb-1" id="resultCount">-</div>
                        <small class="text-muted">Results Found</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-info mb-1" id="currentPage">-</div>
                        <small class="text-muted">Current Page</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-warning mb-1" id="totalPages">-</div>
                        <small class="text-muted">Total Pages</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Work Orders Results Cards -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-dark text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-th-large me-2"></i>Search Results (<span id="workorderCount">0</span>)
                </h5>
                <div class="d-flex align-items-center">
                    <div class="btn-group me-3" role="group">
                        <button class="btn btn-outline-light btn-sm" id="cardViewBtn" onclick="toggleView('cards')" title="Card View">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm" id="tableViewBtn" onclick="toggleView('table')" title="Table View">
                            <i class="fas fa-table"></i>
                        </button>
                    </div>
                    <button class="btn btn-outline-light btn-sm me-2" id="selectAllBtn">
                        <i class="fas fa-check-square me-1"></i><span class="d-none d-md-inline">Select All</span>
                    </button>
                    <button class="btn btn-outline-light btn-sm" id="clearSelectionBtn">
                        <i class="fas fa-square me-1"></i><span class="d-none d-md-inline">Clear All</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-3">
            <!-- Work Order Cards Container -->
            <div id="workordersCardsContainer" class="row g-3">
                <!-- Dynamic card content will be inserted here -->
            </div>

            <!-- Legacy Table (Hidden by default, can be toggled) -->
            <div id="legacyTableContainer" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="workordersTable">
                        <thead class="table-light">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAllCheckbox" class="form-check-input">
                                </th>
                                <th>Work Order</th>
                                <th>Site</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Priority</th>
                                <th>Materials</th>
                                <th>Work Type</th>
                                <th>Assigned To</th>
                                <th>Location</th>
                                <th>Asset</th>
                                <th>Report Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="workordersTableBody">
                            <!-- Dynamic content will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Pagination -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6 mb-3 mb-md-0">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary me-2">
                            <i class="fas fa-list me-1"></i>
                            Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
                        </span>
                        <span class="text-muted">
                            Showing <span id="showingFrom">0</span>-<span id="showingTo">0</span>
                            of <span id="totalResults">0</span> work orders
                        </span>
                    </div>
                </div>
                <div class="col-md-6">
                    <nav aria-label="Work order pagination" class="d-flex justify-content-md-end">
                        <ul class="pagination mb-0" id="pagination">
                            <!-- Dynamic pagination will be inserted here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Initial Empty State -->
<div id="emptyState" class="card border-0 shadow-sm mb-4">
    <div class="card-body text-center p-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Ready to Search</h5>
        <p class="text-muted">
            Use the search filters above to find work orders.
            Results will be displayed here with lightning-fast performance.
        </p>
        <div class="alert alert-light border">
            <small>
                <i class="fas fa-lightbulb text-warning me-1"></i>
                <strong>Tip:</strong> Select one or more sites and add filters for best results.
                You can search across multiple sites simultaneously. Use the Work Order # field for exact or partial number matching.
                All searches exclude tasks and history records.
            </small>
        </div>
    </div>
</div>

<!-- Navigation -->
<div class="text-center mt-4 mb-5">
    <a href="{{ url_for('welcome') }}" class="btn btn-outline-primary me-2">
        <i class="fas fa-arrow-left me-2"></i>Back to Welcome
    </a>
    <a href="{{ url_for('enhanced_profile') }}" class="btn btn-outline-success me-2">
        <i class="fas fa-rocket me-2"></i>Enhanced Profile
    </a>
    <a href="/api-docs" class="btn btn-outline-info me-2">
        <i class="fas fa-code me-2"></i>API Docs
    </a>
    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
        <i class="fas fa-sign-out-alt me-2"></i>Logout
    </a>
</div>

<script>
let currentPage = 1;
let currentSearchCriteria = {};
let isSearching = false;
let currentView = 'cards'; // 'cards' or 'table'
let currentWorkorders = []; // Store current workorders for view switching

document.addEventListener('DOMContentLoaded', function() {
    // Load available sites first
    loadAvailableSites();

    // Initialize search form
    const searchForm = document.getElementById('searchForm');
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        performSearch(1); // Start from page 1
    });

    // Initialize selection handlers
    document.getElementById('selectAllBtn').addEventListener('click', selectAllWorkOrders);
    document.getElementById('clearSelectionBtn').addEventListener('click', clearAllSelections);
    document.getElementById('selectAllCheckbox').addEventListener('change', toggleAllWorkOrders);
});

async function loadAvailableSites() {
    try {
        const response = await fetch('/api/enhanced-workorders/available-sites');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        if (result.success) {
            populateSiteDropdown(result.sites, result.default_site);
        } else {
            console.error('Failed to load sites:', result.error);
            showSiteLoadError();
        }
    } catch (error) {
        console.error('Error loading available sites:', error);
        showSiteLoadError();
    }
}

function populateSiteDropdown(sites, defaultSite) {
    const siteFilter = document.getElementById('siteFilter');

    // Clear existing options
    siteFilter.innerHTML = '';

    if (sites.length === 0) {
        siteFilter.innerHTML = '<option value="">No sites available</option>';
        return;
    }

    // Add sites as options
    sites.forEach(site => {
        const option = document.createElement('option');
        option.value = site.siteid;
        option.textContent = `${site.siteid}${site.description !== site.siteid ? ' - ' + site.description : ''}`;

        // Pre-select the user's default site
        if (site.siteid === defaultSite) {
            option.selected = true;
        }

        siteFilter.appendChild(option);
    });

    console.log(`✅ Loaded ${sites.length} available sites, default: ${defaultSite}`);
}

function showSiteLoadError() {
    const siteFilter = document.getElementById('siteFilter');
    siteFilter.innerHTML = '<option value="">Error loading sites</option>';
}

async function performSearch(page = 1) {
    if (isSearching) return;

    isSearching = true;
    currentPage = page;

    // Get search criteria from form
    const formData = new FormData(document.getElementById('searchForm'));
    currentSearchCriteria = {};

    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            if (key === 'site_ids') {
                // Handle multiple site selection
                if (!currentSearchCriteria[key]) {
                    currentSearchCriteria[key] = [];
                }
                currentSearchCriteria[key].push(value.trim());
            } else {
                currentSearchCriteria[key] = value.trim();
            }
        }
    }

    // Log selected sites for debugging
    if (currentSearchCriteria.site_ids) {
        console.log(`🏢 Selected sites: ${currentSearchCriteria.site_ids.join(', ')}`);
    }

    // Show loading state
    showLoadingState();

    try {
        const startTime = Date.now();

        const response = await fetch('/api/enhanced-workorders/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                search_criteria: currentSearchCriteria,
                page: page,
                page_size: 10
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        const searchTime = (Date.now() - startTime) / 1000;

        displaySearchResults(result, searchTime);

    } catch (error) {
        console.error('Search error:', error);
        showErrorState(error.message);
    } finally {
        isSearching = false;
    }
}

function showLoadingState() {
    // Hide empty state
    document.getElementById('emptyState').style.display = 'none';

    // Show search results container
    const searchResults = document.getElementById('searchResults');
    searchResults.style.display = 'block';

    // Show loading in cards container
    const cardsContainer = document.getElementById('workordersCardsContainer');
    cardsContainer.innerHTML = `
        <div class="col-12">
            <div class="text-center p-5">
                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;"></div>
                <h5>Searching work orders...</h5>
                <p class="text-muted">Please wait while we fetch your results</p>
            </div>
        </div>
    `;

    // Update metrics with loading state
    document.getElementById('searchTime').textContent = '...';
    document.getElementById('resultCount').textContent = '...';
    document.getElementById('currentPage').textContent = '...';
    document.getElementById('totalPages').textContent = '...';
    document.getElementById('workorderCount').textContent = '...';
}

function displaySearchResults(result, searchTime) {
    // Update performance metrics
    document.getElementById('searchTime').textContent = searchTime.toFixed(3) + 's';
    document.getElementById('resultCount').textContent = result.workorders.length;
    document.getElementById('currentPage').textContent = result.page;
    document.getElementById('totalPages').textContent = result.total_pages;
    document.getElementById('workorderCount').textContent = result.workorders.length;

    // Update pagination info
    const showingFrom = result.workorders.length > 0 ? ((result.page - 1) * result.page_size) + 1 : 0;
    const showingTo = Math.min(result.page * result.page_size, result.total_count);
    document.getElementById('showingFrom').textContent = showingFrom;
    document.getElementById('showingTo').textContent = showingTo;
    document.getElementById('totalResults').textContent = result.total_count;

    // Store current workorders and populate based on current view
    currentWorkorders = result.workorders;
    if (currentView === 'cards') {
        populateWorkOrdersCards(result.workorders);
    } else {
        populateWorkOrdersTable(result.workorders);
    }

    // Update pagination
    updatePagination(result);

    // Show results
    document.getElementById('searchResults').style.display = 'block';
    document.getElementById('emptyState').style.display = 'none';
}

function populateWorkOrdersCards(workorders) {
    const cardsContainer = document.getElementById('workordersCardsContainer');

    if (workorders.length === 0) {
        cardsContainer.innerHTML = `
            <div class="col-12">
                <div class="text-center p-5 text-muted">
                    <i class="fas fa-search fa-3x mb-3 opacity-50"></i>
                    <h5>No work orders found</h5>
                    <p>No work orders match your search criteria. Try adjusting your filters.</p>
                </div>
            </div>
        `;
        return;
    }

    cardsContainer.innerHTML = workorders.map(wo => `
        <div class="col-lg-6 col-xl-4">
            <div class="work-order-card">
                <div class="priority-indicator-card priority-${wo.priority || 3}"></div>
                <input type="checkbox" class="form-check-input work-order-checkbox"
                       value="${wo.wonum}" onchange="updateSelectedCount()">

                <div class="work-order-card-header">
                    <a href="/enhanced-workorder-details/${wo.wonum}" class="work-order-number">
                        ${wo.wonum}
                    </a>
                    <div class="work-order-site">
                        <i class="fas fa-map-marker-alt me-1"></i>${wo.siteid || 'Unknown Site'}
                    </div>
                </div>

                <div class="work-order-card-body">
                    <div class="work-order-description">
                        ${wo.description || 'No description available'}
                    </div>

                    <div class="work-order-meta">
                        <div class="meta-item">
                            <div class="meta-label">Status</div>
                            <div class="meta-value">${getStatusBadge(wo.status)}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Priority</div>
                            <div class="meta-value">${getPriorityBadge(wo.priority)}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Work Type</div>
                            <div class="meta-value">${wo.worktype || '-'}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Assigned To</div>
                            <div class="meta-value">${wo.assignedto || 'Unassigned'}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Location</div>
                            <div class="meta-value">${wo.location || '-'}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Asset</div>
                            <div class="meta-value">${wo.assetnum || '-'}</div>
                        </div>
                    </div>

                    <div class="work-order-actions">
                        <button class="card-action-btn btn-success" onclick="executeIndividualMethod('approve', '${wo.wonum}')" title="Approve Work Order">
                            <i class="fas fa-check"></i>
                            <span>Approve</span>
                        </button>
                        <button class="card-action-btn btn-primary" onclick="executeIndividualMethod('start', '${wo.wonum}')" title="Start Work Order">
                            <i class="fas fa-play"></i>
                            <span>Start</span>
                        </button>
                        <button class="card-action-btn btn-warning" onclick="executeIndividualMethod('complete', '${wo.wonum}')" title="Complete Work Order">
                            <i class="fas fa-flag-checkered"></i>
                            <span>Complete</span>
                        </button>
                        <a href="/enhanced-workorder-details/${wo.wonum}" class="card-action-btn btn-info" title="View Work Order Details">
                            <i class="fas fa-eye"></i>
                            <span>Details</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `).join('');

    // Reset selection count
    updateSelectedCount();
}

// Keep the legacy table function for backward compatibility
function populateWorkOrdersTable(workorders) {
    const tableBody = document.getElementById('workordersTableBody');

    if (workorders.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="13" class="text-center p-4 text-muted">
                    <i class="fas fa-search me-2"></i>No work orders found matching your search criteria.
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = workorders.map(wo => `
        <tr>
            <td>
                <input type="checkbox" class="form-check-input work-order-checkbox"
                       value="${wo.wonum}" onchange="updateSelectedCount()">
            </td>
            <td>
                <a href="/enhanced-workorder-details/${wo.wonum}" class="text-decoration-none">
                    <strong class="text-primary">${wo.wonum}</strong>
                </a>
            </td>
            <td>
                <span class="badge bg-secondary">${wo.siteid || '-'}</span>
            </td>
            <td>
                <div class="text-truncate" style="max-width: 200px;" title="${wo.description || ''}">
                    ${wo.description || '-'}
                </div>
            </td>
            <td>${getStatusBadge(wo.status)}</td>
            <td>${getPriorityBadge(wo.priority)}</td>
            <td>
                <div id="materials-${wo.wonum}" class="materials-check-container">
                    <button class="btn btn-sm btn-outline-secondary materials-check-btn"
                            onclick="checkMaterials('${wo.wonum}', '${wo.siteid}')"
                            title="Check for planned materials">
                        <i class="fas fa-boxes"></i>
                        <span class="d-none d-md-inline ms-1">Check Materials</span>
                    </button>
                </div>
            </td>
            <td>${wo.worktype || '-'}</td>
            <td>${wo.assignedto || '-'}</td>
            <td>${wo.location || '-'}</td>
            <td>${wo.assetnum || '-'}</td>
            <td>
                ${wo.reportdate ? `<small>${wo.reportdate.substring(0, 10)}</small>` : '<span class="text-muted">-</span>'}
            </td>
            <td>
                <!-- Desktop Actions -->
                <div class="btn-group desktop-actions" role="group">
                    <button class="btn btn-sm btn-success" onclick="executeIndividualMethod('approve', '${wo.wonum}')" title="Approve">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="executeIndividualMethod('start', '${wo.wonum}')" title="Start">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="executeIndividualMethod('complete', '${wo.wonum}')" title="Complete">
                        <i class="fas fa-flag-checkered"></i>
                    </button>
                    <a href="/enhanced-workorder-details/${wo.wonum}" class="btn btn-sm btn-outline-info" title="View Details">
                        <i class="fas fa-eye"></i>
                    </a>
                </div>

                <!-- Mobile Actions -->
                <div class="mobile-action-grid">
                    <button class="mobile-action-btn btn-success" onclick="executeIndividualMethod('approve', '${wo.wonum}')" title="Approve Work Order">
                        <i class="fas fa-check"></i>
                        <span>Approve</span>
                    </button>
                    <button class="mobile-action-btn btn-primary" onclick="executeIndividualMethod('start', '${wo.wonum}')" title="Start Work Order">
                        <i class="fas fa-play"></i>
                        <span>Start</span>
                    </button>
                    <button class="mobile-action-btn btn-warning" onclick="executeIndividualMethod('complete', '${wo.wonum}')" title="Complete Work Order">
                        <i class="fas fa-flag-checkered"></i>
                        <span>Complete</span>
                    </button>
                    <a href="/enhanced-workorder-details/${wo.wonum}" class="mobile-action-btn btn-info" title="View Work Order Details">
                        <i class="fas fa-eye"></i>
                        <span>Details</span>
                    </a>
                </div>
            </td>
        </tr>
    `).join('');

    // Reset selection count
    updateSelectedCount();
}

function getStatusBadge(status) {
    if (!status) return '<span class="text-muted">-</span>';

    const statusClasses = {
        'APPR': 'bg-success', 'READY': 'bg-success',
        'ASSIGN': 'bg-primary', 'INPRG': 'bg-primary',
        'WAPPR': 'bg-warning', 'WGOVT': 'bg-warning', 'WMATL': 'bg-warning', 'WSERV': 'bg-warning', 'WSCH': 'bg-warning',
        'PACK': 'bg-secondary', 'DEFER': 'bg-secondary'
    };

    const badgeClass = statusClasses[status] || 'bg-info';
    return `<span class="badge ${badgeClass}">${status}</span>`;
}

function getPriorityBadge(priority) {
    if (!priority) return '<span class="text-muted">-</span>';

    const priorityNum = parseInt(priority);
    let badgeClass = 'bg-success';

    if (priorityNum <= 2) badgeClass = 'bg-danger';
    else if (priorityNum <= 3) badgeClass = 'bg-warning';

    return `<span class="badge ${badgeClass}">${priority}</span>`;
}

function updatePagination(result) {
    const pagination = document.getElementById('pagination');

    if (result.total_pages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // First page button (mobile-friendly)
    if (result.page > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="performSearch(1)" title="First page">
                    <i class="fas fa-angle-double-left"></i>
                </a>
            </li>
        `;
    }

    // Previous button
    if (result.has_prev) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="performSearch(${result.page - 1})" title="Previous page">
                    <i class="fas fa-angle-left"></i>
                    <span class="d-none d-sm-inline ms-1">Previous</span>
                </a>
            </li>
        `;
    }

    // Page numbers (show fewer on mobile)
    const isMobile = window.innerWidth < 768;
    const pageRange = isMobile ? 1 : 2;
    const startPage = Math.max(1, result.page - pageRange);
    const endPage = Math.min(result.total_pages, result.page + pageRange);

    // Show ellipsis if there are pages before startPage
    if (startPage > 1) {
        paginationHTML += `
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
        `;
    }

    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === result.page;
        paginationHTML += `
            <li class="page-item ${isActive ? 'active' : ''}">
                <a class="page-link" href="#" onclick="performSearch(${i})">${i}</a>
            </li>
        `;
    }

    // Show ellipsis if there are pages after endPage
    if (endPage < result.total_pages) {
        paginationHTML += `
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
        `;
    }

    // Next button
    if (result.has_next) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="performSearch(${result.page + 1})" title="Next page">
                    <span class="d-none d-sm-inline me-1">Next</span>
                    <i class="fas fa-angle-right"></i>
                </a>
            </li>
        `;
    }

    // Last page button (mobile-friendly)
    if (result.page < result.total_pages) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="performSearch(${result.total_pages})" title="Last page">
                    <i class="fas fa-angle-double-right"></i>
                </a>
            </li>
        `;
    }

    pagination.innerHTML = paginationHTML;
}

function showErrorState(errorMessage) {
    const cardsContainer = document.getElementById('workordersCardsContainer');
    cardsContainer.innerHTML = `
        <div class="col-12">
            <div class="text-center p-5 text-danger">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h5>Search Error</h5>
                <p>Error: ${errorMessage}</p>
                <button class="btn btn-outline-primary" onclick="location.reload()">
                    <i class="fas fa-refresh me-2"></i>Retry
                </button>
            </div>
        </div>
    `;
}

// Selection management functions
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.work-order-checkbox:checked');
    const count = checkboxes.length;

    // Update select all checkbox state
    const allCheckboxes = document.querySelectorAll('.work-order-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    if (count === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (count === allCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

function toggleAllWorkOrders() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.work-order-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedCount();
}

function selectAllWorkOrders() {
    const checkboxes = document.querySelectorAll('.work-order-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function clearAllSelections() {
    const checkboxes = document.querySelectorAll('.work-order-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

// Materials availability checking
async function checkMaterials(wonum, siteid) {
    const container = document.getElementById(`materials-${wonum}`);
    const button = container.querySelector('.materials-check-btn');

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';

    try {
        const startTime = Date.now();
        const response = await fetch(`/api/workorder/${wonum}/materials-availability`);
        const result = await response.json();
        const checkTime = (Date.now() - startTime) / 1000;

        if (result.success) {
            const availability = result.availability;

            if (availability.has_materials) {
                // Show green badge with count
                container.innerHTML = `
                    <span class="badge bg-success materials-badge" title="Found ${availability.total_materials} materials across ${availability.tasks_with_materials} tasks${availability.cache_hit ? ' (cached)' : ''}">
                        <i class="fas fa-boxes me-1"></i>
                        ${availability.total_materials} Material <br> Request${availability.total_materials !== 1 ? 's' : ''}
                    </span>
                `;
            } else {
                // Show gray badge indicating no materials
                container.innerHTML = `
                    <span class="badge bg-secondary materials-badge" title="No planned materials found${availability.cache_hit ? ' (cached)' : ''}">
                        <i class="fas fa-box-open me-1"></i>
                        No Materials
                    </span>
                `;
            }

            console.log(`📦 Materials check for WO ${wonum}: ${availability.total_materials} materials in ${checkTime.toFixed(3)}s${availability.cache_hit ? ' (cached)' : ''}`);

        } else {
            // Show error state
            container.innerHTML = `
                <span class="badge bg-danger materials-badge" title="Error checking materials: ${result.error}">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Error
                </span>
            `;
        }

    } catch (error) {
        console.error('Error checking materials:', error);
        // Show error state
        container.innerHTML = `
            <span class="badge bg-danger materials-badge" title="Network error: ${error.message}">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Error
            </span>
        `;
    }
}

// Individual work order actions
async function executeIndividualMethod(methodName, wonum) {
    if (!confirm(`Are you sure you want to execute "${methodName}" on work order ${wonum}?`)) {
        return;
    }

    try {
        const response = await fetch(`/api/workorder/${wonum}/${methodName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });

        const result = await response.json();

        if (result.success) {
            alert(`Successfully executed ${methodName} on work order ${wonum}`);
            // Refresh current search
            performSearch(currentPage);
        } else {
            alert(`Error executing ${methodName}: ${result.error || 'Unknown error'}`);
        }

    } catch (error) {
        alert(`Network error: ${error.message}`);
    }
}

// Function to refresh materials check for a specific work order
async function refreshMaterialsCheck(wonum, siteid) {
    console.log(`🔄 Refreshing materials check for WO ${wonum}`);

    try {
        // Clear materials cache first
        const cacheResponse = await fetch('/api/task/planned-materials/cache/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const cacheResult = await cacheResponse.json();
        if (cacheResult.success) {
            console.log('✅ Materials cache cleared, re-checking materials...');

            // Re-run the materials check
            await checkMaterials(wonum, siteid);
        } else {
            console.error('❌ Failed to clear materials cache:', cacheResult.error);
        }
    } catch (error) {
        console.error('❌ Error refreshing materials check:', error);
    }
}

// Global function to refresh all materials checks (called after material addition)
async function refreshAllMaterialsChecks() {
    console.log('🔄 Refreshing all materials checks after material addition...');

    try {
        // Clear materials cache first
        const cacheResponse = await fetch('/api/task/planned-materials/cache/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const cacheResult = await cacheResponse.json();
        if (cacheResult.success) {
            console.log('✅ Materials cache cleared for all work orders');

            // Find all materials badges that are currently showing data (not buttons)
            const materialsBadges = document.querySelectorAll('.materials-badge');
            materialsBadges.forEach(badge => {
                const container = badge.closest('.materials-check-container');
                if (container) {
                    const containerId = container.id;
                    const wonum = containerId.replace('materials-', '');

                    // Reset to button state for re-checking
                    container.innerHTML = `
                        <button class="btn btn-sm btn-outline-secondary materials-check-btn"
                                onclick="checkMaterials('${wonum}', '')"
                                title="Check for planned materials">
                            <i class="fas fa-boxes"></i>
                            <span class="d-none d-md-inline ms-1">Check Materials</span>
                        </button>
                    `;
                }
            });

            console.log('🔄 Reset materials check buttons - user can re-check as needed');
        } else {
            console.error('❌ Failed to clear materials cache:', cacheResult.error);
        }
    } catch (error) {
        console.error('❌ Error refreshing all materials checks:', error);
    }
}

// Toggle between card and table views
function toggleView(viewType) {
    currentView = viewType;

    // Update button states
    const cardBtn = document.getElementById('cardViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');

    if (viewType === 'cards') {
        cardBtn.classList.remove('btn-outline-light');
        cardBtn.classList.add('btn-light');
        tableBtn.classList.remove('btn-light');
        tableBtn.classList.add('btn-outline-light');

        // Show cards, hide table
        document.getElementById('workordersCardsContainer').style.display = 'block';
        document.getElementById('legacyTableContainer').style.display = 'none';

        // Populate cards if we have data
        if (currentWorkorders.length > 0) {
            populateWorkOrdersCards(currentWorkorders);
        }
    } else {
        tableBtn.classList.remove('btn-outline-light');
        tableBtn.classList.add('btn-light');
        cardBtn.classList.remove('btn-light');
        cardBtn.classList.add('btn-outline-light');

        // Show table, hide cards
        document.getElementById('workordersCardsContainer').style.display = 'none';
        document.getElementById('legacyTableContainer').style.display = 'block';

        // Populate table if we have data
        if (currentWorkorders.length > 0) {
            populateWorkOrdersTable(currentWorkorders);
        }
    }
}

// Initialize view on page load
document.addEventListener('DOMContentLoaded', function() {
    // Set initial view state
    toggleView('cards');
});
</script>

{% endblock %}
